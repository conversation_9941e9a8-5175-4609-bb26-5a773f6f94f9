/* CSS Değişkenleri */
:root {
    /* Renkle<PERSON> - <PERSON><PERSON><PERSON><PERSON> */
    --color-primary: #2563eb;
    --color-primary-dark: #1d4ed8;
    --color-secondary: #64748b;
    --color-background: #ffffff;
    --color-surface: #f8fafc;
    --color-text: #1e293b;
    --color-text-light: #64748b;
    --color-border: #e2e8f0;
    --color-success: #22c55e;
    --color-error: #ef4444;
    --color-warning: #f59e0b;
    --color-info: #3b82f6;

    /* <PERSON><PERSON><PERSON><PERSON> */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);

    /* <PERSON><PERSON><PERSON>ipleri */
    --font-primary: 'Inter', system-ui, -apple-system, sans-serif;
    --font-secondary: 'Poppins', system-ui, -apple-system, sans-serif;

    /* Boşluklar */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 1rem;
    --radius-full: 9999px;

    /* Geçişler */
    --transition-fast: 150ms ease;
    --transition-normal: 300ms ease;
    --transition-slow: 500ms ease;

    /* Hata Mesajı */
    --error-bg: #fee2e2;
    --error-color: #dc2626;
}

/* Karanlık Tema */
[data-theme="dark"] {
    --color-primary: #3b82f6;
    --color-primary-dark: #2563eb;
    --color-secondary: #94a3b8;
    --color-background: #0f172a;
    --color-surface: #1e293b;
    --color-text: #f1f5f9;
    --color-text-light: #cbd5e1;
    --color-border: #334155;
    --error-bg: #7f1d1d;
    --error-color: #fecaca;
}

/* Reset ve Temel Stiller */
*, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    line-height: 1.5;
    color: var(--color-text);
    background-color: var(--color-background);
    transition: background-color var(--transition-normal);
}

/* Tipografi */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-secondary);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: var(--spacing-md);
}

a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--color-primary-dark);
}

/* Layout */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Navigasyon */
.nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: var(--color-background);
    box-shadow: var(--shadow-sm);
    z-index: 1000;
    transition: background-color var(--transition-normal);
}

.nav__container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 4rem;
    padding: 0 var(--spacing-lg);
}

.nav__logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-text);
}

.nav__menu {
    display: flex;
    gap: var(--spacing-lg);
    list-style: none;
}

.nav__link {
    color: var(--color-text);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.nav__link:hover,
.nav__link--active {
    color: var(--color-primary);
    background-color: var(--color-surface);
}

.nav__theme-toggle {
    background: none;
    border: none;
    color: var(--color-text);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-full);
    transition: background-color var(--transition-fast);
}

.nav__theme-toggle:hover {
    background-color: var(--color-surface);
}

.nav__link--admin {
    background-color: var(--color-primary) !important;
    color: white !important;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.nav__link--admin:hover {
    background-color: var(--color-primary-dark) !important;
}

/* Hero Bölümü */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: var(--spacing-2xl) 0;
    background-color: var(--color-surface);
}

.hero__content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

@media (min-width: 768px) {
    .hero__content {
        grid-template-columns: 3fr 2fr;
    }
}

.hero__text {
    max-width: 600px;
}

.hero__title {
    font-size: 3.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--color-text);
}

.hero__subtitle {
    font-size: 1.5rem;
    color: var(--color-text-light);
    margin-bottom: var(--spacing-lg);
}

.hero__cta {
    display: inline-block;
    padding: var(--spacing-md) var(--spacing-xl);
    background-color: var(--color-primary);
    color: white;
    border-radius: var(--radius-md);
    font-weight: 500;
    transition: all var(--transition-fast);
}

.hero__cta:hover {
    background-color: var(--color-primary-dark);
    transform: translateY(-2px);
}

.hero__image {
    position: relative;
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
}

.hero__photo {
    width: 100%;
    height: auto;
    border-radius: var(--radius-lg);
    transition: transform var(--transition-normal);
}

.hero__photo:hover {
    transform: scale(1.05);
}

/* Hakkımda Bölümü */
.about {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: var(--spacing-2xl) 0;
    background-color: var(--color-background);
}

.about__grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

@media (min-width: 768px) {
    .about__grid {
        grid-template-columns: 3fr 2fr;
    }
}

.about__content {
    max-width: 600px;
    text-align: center;
}

.about__skills {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.skill {
    background-color: var(--color-surface);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
}

.skill__header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.skill__icon {
    width: 2rem;
    height: 2rem;
    color: var(--color-primary);
}

.skill__name {
    font-weight: 500;
}

.skill__progress {
    height: 0.5rem;
    background-color: var(--color-border);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.skill__bar {
    height: 100%;
    background-color: var(--color-primary);
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
}

/* Projeler Bölümü */
.projects {
    padding: var(--spacing-2xl) 0;
    background-color: var(--color-surface);
}

.projects__header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.projects__filters {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.filter__button {
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-full);
    color: var(--color-text);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter__button:hover,
.filter__button--active {
    background-color: var(--color-primary);
    color: white;
    border-color: var(--color-primary);
}

.projects__calendar {
    background-color: var(--color-background);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
}

/* İletişim Bölümü */
.contact {
    padding: var(--spacing-2xl) 0;
}

.contact__grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
}

@media (min-width: 768px) {
    .contact__grid {
        grid-template-columns: 1fr 2fr;
    }
}

.contact__info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.contact__item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background-color: var(--color-surface);
    border-radius: var(--radius-md);
    transition: transform var(--transition-fast);
}

.contact__item:hover {
    transform: translateX(var(--spacing-sm));
}

.contact__icon {
    width: 1.5rem;
    height: 1.5rem;
    color: var(--color-primary);
}

.contact__form {
    background-color: var(--color-surface);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.form__group {
    margin-bottom: var(--spacing-lg);
}

.form__label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--color-text);
    font-weight: 500;
}

.form__input {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    background-color: var(--color-background);
    color: var(--color-text);
    transition: all var(--transition-fast);
}

.form__input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px var(--color-primary);
}

.form__textarea {
    min-height: 150px;
    resize: vertical;
}

.form__button {
    width: 100%;
    padding: var(--spacing-md);
    background-color: var(--color-primary);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.form__button:hover {
    background-color: var(--color-primary-dark);
}

/* Footer */
.footer {
    background-color: var(--color-surface);
    padding: var(--spacing-xl) 0;
    margin-top: var(--spacing-2xl);
}

.footer__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
    text-align: center;
}

.footer__social {
    display: flex;
    gap: var(--spacing-md);
}

.social__link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background-color: var(--color-background);
    border-radius: var(--radius-full);
    color: var(--color-text);
    transition: all var(--transition-fast);
}

.social__link:hover {
    background-color: var(--color-primary);
    color: white;
    transform: translateY(-2px);
}

.footer__back-to-top {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    width: 3rem;
    height: 3rem;
    background-color: var(--color-primary);
    color: white;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.footer__back-to-top--visible {
    opacity: 1;
    visibility: visible;
}

.footer__back-to-top:hover {
    background-color: var(--color-primary-dark);
    transform: translateY(-2px);
}

/* Yardımcı Sınıflar */
.text-center { text-align: center; }
.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-secondary); }
.bg-surface { background-color: var(--color-surface); }
.mt-1 { margin-top: var(--spacing-sm); }
.mt-2 { margin-top: var(--spacing-md); }
.mt-3 { margin-top: var(--spacing-lg); }
.mt-4 { margin-top: var(--spacing-xl); }
.mb-1 { margin-bottom: var(--spacing-sm); }
.mb-2 { margin-bottom: var(--spacing-md); }
.mb-3 { margin-bottom: var(--spacing-lg); }
.mb-4 { margin-bottom: var(--spacing-xl); }

/* Animasyonlar */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
    animation: fadeIn var(--transition-normal) ease-out forwards;
}

/* Responsive Tasarım */
@media (max-width: 768px) {
    html { font-size: 14px; }

    .nav__menu {
        display: none;
    }

    .nav__menu--mobile {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--color-background);
        padding: var(--spacing-md);
        box-shadow: var(--shadow-md);
    }

    .hero__title {
        font-size: 2.5rem;
    }

    .hero__subtitle {
        font-size: 1.25rem;
    }

    .about__grid,
    .contact__grid {
        grid-template-columns: 1fr;
    }

    .projects__filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter__button {
        width: 100%;
    }
}

/* Erişilebilirlik */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Yazdırma Stilleri */
@media print {
    .nav,
    .footer__back-to-top,
    .contact__form {
        display: none;
    }

    body {
        color: black;
        background: white;
    }

    a {
        text-decoration: underline;
    }
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal--visible {
    opacity: 1;
    visibility: visible;
}

.modal__content {
    background-color: var(--color-background);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    transform: translateY(20px);
    transition: transform var(--transition-normal);
}

.modal--visible .modal__content {
    transform: translateY(0);
}

.modal__close {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: none;
    border: none;
    color: var(--color-text);
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-full);
    transition: all var(--transition-fast);
}

.modal__close:hover {
    background-color: var(--color-surface);
    color: var(--color-primary);
}

.modal__header {
    margin-bottom: var(--spacing-lg);
}

.modal__title {
    font-size: 1.75rem;
    margin-bottom: var(--spacing-xs);
}

.modal__date {
    color: var(--color-text-light);
    font-size: 0.875rem;
}

.modal__body {
    display: grid;
    gap: var(--spacing-lg);
}

.modal__image {
    width: 100%;
    border-radius: var(--radius-md);
    overflow: hidden;
}

.modal__img {
    width: 100%;
    height: auto;
    object-fit: cover;
}

.modal__description {
    color: var(--color-text);
    line-height: 1.6;
}

.modal__tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.tag {
    background-color: var(--color-surface);
    color: var(--color-text);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 500;
}

.modal__links {
    display: flex;
    gap: var(--spacing-md);
}

.modal__link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--color-surface);
    color: var(--color-text);
    border-radius: var(--radius-md);
    font-weight: 500;
    transition: all var(--transition-fast);
}

.modal__link:hover {
    background-color: var(--color-primary);
    color: white;
    transform: translateY(-2px);
}

/* Timeline */
.timeline {
    position: relative;
    padding: var(--spacing-lg) 0;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 1.5rem;
    bottom: 0;
    width: 2px;
    background-color: var(--color-border);
}

.timeline__item {
    position: relative;
    padding-left: 3.5rem;
    margin-bottom: var(--spacing-xl);
}

.timeline__item:last-child {
    margin-bottom: 0;
}

.timeline__icon {
    position: absolute;
    left: 0;
    top: 0;
    width: 3rem;
    height: 3rem;
    background-color: var(--color-primary);
    color: white;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.timeline__content {
    background-color: var(--color-surface);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
}

.timeline__content h3 {
    margin-bottom: var(--spacing-xs);
    color: var(--color-text);
}

.timeline__content p {
    color: var(--color-text-light);
    margin-bottom: var(--spacing-xs);
}

.timeline__date {
    font-size: 0.875rem;
    color: var(--color-primary);
    font-weight: 500;
}

/* Form Success */
.form__success {
    text-align: center;
    padding: var(--spacing-xl);
    background-color: var(--color-surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.form__success i {
    font-size: 3rem;
    color: var(--color-success);
    margin-bottom: var(--spacing-md);
}

.form__success h3 {
    color: var(--color-text);
    margin-bottom: var(--spacing-sm);
}

.form__success p {
    color: var(--color-text-light);
}

/* Status Badge */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 500;
}

.status-badge i {
    font-size: 0.75rem;
}

.status-badge--available {
    background-color: var(--color-success);
    color: white;
}

.status-badge--busy {
    background-color: var(--color-warning);
    color: white;
}

.status-badge--unavailable {
    background-color: var(--color-error);
    color: white;
}

/* Contact Card */
.contact__card {
    background-color: var(--color-surface);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-xl);
}

.contact__availability {
    background-color: var(--color-surface);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
}

.contact__availability h3 {
    margin-bottom: var(--spacing-md);
    color: var(--color-text);
}

.contact__availability p {
    color: var(--color-text-light);
    margin-bottom: var(--spacing-md);
}

.contact__status {
    display: flex;
    justify-content: flex-end;
}

/* Responsive Modal */
@media (max-width: 768px) {
    .modal__content {
        padding: var(--spacing-lg);
        width: 95%;
    }

    .modal__links {
        flex-direction: column;
    }

    .modal__link {
        width: 100%;
        justify-content: center;
    }
}

/* Bildirimler */
.notification {
    position: fixed;
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    background-color: var(--color-surface);
    color: var(--color-text);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    transform: translateY(100%);
    opacity: 0;
    transition: all var(--transition-normal);
}

.notification--visible {
    transform: translateY(0);
    opacity: 1;
}

.notification--success {
    background-color: var(--color-success);
    color: white;
}

.notification--error {
    background-color: var(--color-error);
    color: white;
}

.notification--warning {
    background-color: var(--color-warning);
    color: white;
}

.notification--info {
    background-color: var(--color-info);
    color: white;
}

/* Takvim Etkinlikleri */
.event {
    border: none !important;
    padding: var(--spacing-xs) var(--spacing-sm) !important;
    margin: var(--spacing-xs) 0 !important;
    border-radius: var(--radius-sm) !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all var(--transition-fast) !important;
}

.event:hover {
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-md) !important;
}

.event--meeting {
    background-color: var(--color-primary) !important;
    color: white !important;
}

.event--review {
    background-color: var(--color-info) !important;
    color: white !important;
}

.event--deadline {
    background-color: var(--color-warning) !important;
    color: white !important;
}

.event--presentation {
    background-color: var(--color-success) !important;
    color: white !important;
}

.event--other {
    background-color: var(--color-surface) !important;
    color: var(--color-text) !important;
}

.event__icon {
    margin-right: var(--spacing-xs) !important;
    font-size: 0.75rem !important;
}

/* Takvim Filtreleri */
.calendar__filters {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

.calendar__filter {
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-full);
    background-color: var(--color-surface);
    color: var(--color-text);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.calendar__filter:hover {
    background-color: var(--color-primary);
    color: white;
}

.calendar__filter--active {
    background-color: var(--color-primary);
    color: white;
}

/* Takvim Modal */
.calendar-modal .modal__content {
    max-width: 500px;
}

.calendar-modal__form {
    display: grid;
    gap: var(--spacing-md);
}

.calendar-modal__field {
    display: grid;
    gap: var(--spacing-xs);
}

.calendar-modal__label {
    font-weight: 500;
    color: var(--color-text);
}

.calendar-modal__input,
.calendar-modal__select,
.calendar-modal__textarea {
    padding: var(--spacing-sm);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    background-color: var(--color-background);
    color: var(--color-text);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.calendar-modal__input:focus,
.calendar-modal__select:focus,
.calendar-modal__textarea:focus {
    border-color: var(--color-primary);
    outline: none;
    box-shadow: 0 0 0 2px var(--color-primary-light);
}

.calendar-modal__textarea {
    min-height: 100px;
    resize: vertical;
}

.calendar-modal__buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    margin-top: var(--spacing-md);
}

.calendar-modal__button {
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.calendar-modal__button--primary {
    background-color: var(--color-primary);
    color: white;
    border: none;
}

.calendar-modal__button--primary:hover {
    background-color: var(--color-primary-dark);
}

.calendar-modal__button--secondary {
    background-color: var(--color-surface);
    color: var(--color-text);
    border: 1px solid var(--color-border);
}

.calendar-modal__button--secondary:hover {
    background-color: var(--color-border);
}

/* Responsive Takvim */
@media (max-width: 768px) {
    .calendar__filters {
        justify-content: center;
    }

    .calendar-modal .modal__content {
        width: 95%;
        padding: var(--spacing-md);
    }

    .calendar-modal__buttons {
        flex-direction: column;
    }

    .calendar-modal__button {
        width: 100%;
    }
}

/* Zaman Çizelgesi */
.timeline-section {
    padding: var(--spacing-2xl) 0;
    background-color: var(--color-background);
}

.timeline-section__header {
    margin-bottom: var(--spacing-2xl);
}

.timeline__period {
    position: relative;
    padding: var(--spacing-xl) 0;
    margin-bottom: var(--spacing-2xl);
}

.timeline__period:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 2rem;
    bottom: 0;
    width: 2px;
    height: calc(100% - 4rem);
    background-color: var(--color-border);
}

.timeline__period-header {
    position: relative;
    padding-left: 4rem;
    margin-bottom: var(--spacing-xl);
}

.timeline__period-header::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 2rem;
    height: 2rem;
    background-color: var(--color-primary);
    border-radius: var(--radius-full);
    z-index: 1;
}

.timeline__period-header h2 {
    font-size: 1.75rem;
    color: var(--color-text);
    margin-bottom: var(--spacing-xs);
}

.timeline__period-subtitle {
    color: var(--color-text-light);
    font-size: 1.125rem;
}

.timeline__projects {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    padding-left: 4rem;
}

.project-card {
    background-color: var(--color-surface);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.project-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.project-card__header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.project-card__icon {
    width: 2rem;
    height: 2rem;
    color: var(--color-primary);
    font-size: 1.5rem;
}

.project-card__header h3 {
    font-size: 1.25rem;
    color: var(--color-text);
    margin: 0;
}

.project-card__description {
    color: var(--color-text-light);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.project-card__tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.project-card .tag {
    background-color: var(--color-background);
    color: var(--color-text);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Responsive Tasarım */
@media (max-width: 768px) {
    .timeline__period {
        padding: var(--spacing-lg) 0;
    }

    .timeline__period:not(:last-child)::after {
        left: 1.5rem;
    }

    .timeline__period-header {
        padding-left: 3rem;
    }

    .timeline__period-header::before {
        width: 1.5rem;
        height: 1.5rem;
    }

    .timeline__projects {
        padding-left: 3rem;
        grid-template-columns: 1fr;
    }

    .project-card {
        padding: var(--spacing-md);
    }
}

/* Proje Sayfaları */
.projects-section,
.timeline-section {
    padding: 4rem 0;
}

.projects-section__header,
.timeline-section__header {
    margin-bottom: 3rem;
}

.projects-section__header h1,
.timeline-section__header h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--color-text);
}

/* Filtreler */
.projects-section__filters {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--color-surface);
    border-radius: 0.5rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-size: 0.9rem;
    color: var(--color-text-light);
}

.filter-select {
    padding: 0.5rem;
    border: 1px solid var(--color-border);
    border-radius: 0.25rem;
    background: var(--color-background);
    color: var(--color-text);
    min-width: 150px;
}

.filter-select[multiple] {
    height: 100px;
}

#filter-reset {
    align-self: flex-end;
    margin-top: 1.5rem;
}

/* Proje Kartları */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.project-card {
    background: var(--color-surface);
    border-radius: 0.5rem;
    padding: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.project-card__header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.project-card__header i {
    font-size: 1.5rem;
    color: var(--color-primary);
}

.project-card__header h3 {
    font-size: 1.25rem;
    color: var(--color-text);
    margin: 0;
}

.project-card__description {
    color: var(--color-text-light);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.project-card__tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.tag {
    background: var(--color-primary-light);
    color: var(--color-primary);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
}

.project-card__links {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.project-card__link {
    color: var(--color-text-light);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: color 0.3s ease;
}

.project-card__link:hover {
    color: var(--color-primary);
}

.project-card__status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
}

.status-completed {
    background: var(--color-success-light);
    color: var(--color-success);
}

.status-in-progress {
    background: var(--color-warning-light);
    color: var(--color-warning);
}

.status-on-hold {
    background: var(--color-info-light);
    color: var(--color-info);
}

.status-cancelled {
    background: var(--color-error-light);
    color: var(--color-error);
}

/* Zaman Çizelgesi */
.timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 100%;
    background: var(--color-border);
}

.timeline__period {
    position: relative;
    margin-bottom: 3rem;
}

.timeline__period:last-child {
    margin-bottom: 0;
}

.timeline__period-header {
    text-align: center;
    margin-bottom: 2rem;
}

.timeline__period-header h2 {
    font-size: 1.5rem;
    color: var(--color-text);
    margin-bottom: 0.5rem;
}

.timeline__period-subtitle {
    color: var(--color-text-light);
    font-size: 1rem;
}

.timeline__projects {
    position: relative;
    padding: 0 2rem;
}

/* İstatistikler */
.projects-section__stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.stats-card {
    background: var(--color-surface);
    border-radius: 0.5rem;
    padding: 1.5rem;
}

.stats-card h3 {
    font-size: 1.25rem;
    color: var(--color-text);
    margin-bottom: 1.5rem;
}

.stats-chart {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.chart-bar {
    background: var(--color-primary-light);
    border-radius: 0.25rem;
    padding: 0.5rem 1rem;
    position: relative;
    transition: width 0.3s ease;
}

.chart-label {
    color: var(--color-text);
    font-weight: 500;
}

.chart-value {
    position: absolute;
    right: 1rem;
    color: var(--color-text-light);
}

/* Responsive Tasarım */
@media (max-width: 768px) {
    .projects-section__filters {
        flex-direction: column;
    }

    .filter-group {
        width: 100%;
    }

    .filter-select {
        width: 100%;
    }

    #filter-reset {
        width: 100%;
        margin-top: 1rem;
    }

    .timeline::before {
        left: 1rem;
    }

    .timeline__projects {
        padding-left: 3rem;
    }

    .projects-section__stats {
        grid-template-columns: 1fr;
    }
}

/* Form Hata Mesajı */
.form__error {
    background-color: var(--error-bg);
    color: var(--error-color);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    animation: fadeIn 0.3s ease-in-out;
}

.form__error i {
    font-size: 1.25rem;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Yetenekler Bölümü */
.skills-section {
    padding: var(--spacing-2xl) 0;
    background-color: var(--color-background);
}

.skills-section__header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.skills-section__header h1 {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-sm);
    color: var(--color-text);
}

.skills__filters {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.skills__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.skill-card {
    background-color: var(--color-surface);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.skill-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.skill-card__header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.skill-card__icon {
    width: 2.5rem;
    height: 2.5rem;
    color: var(--color-primary);
    font-size: 1.75rem;
}

.skill-card__name {
    font-size: 1.25rem;
    color: var(--color-text);
    margin: 0;
}

.skill-card__progress {
    position: relative;
    height: 0.75rem;
    background-color: var(--color-border);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--spacing-md);
}

.skill-card__bar {
    height: 100%;
    background-color: var(--color-primary);
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
}

.skill-card__percentage {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.875rem;
    color: var(--color-text);
    font-weight: 500;
}

.skill-card__description {
    color: var(--color-text-light);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.skill-card__certification {
    background-color: var(--color-background);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.skill-card__certification h4 {
    font-size: 1rem;
    color: var(--color-text);
    margin-bottom: var(--spacing-sm);
}

.skill-card__certification ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.skill-card__certification li {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
    color: var(--color-text-light);
    font-size: 0.875rem;
}

.skill-card__certification strong {
    color: var(--color-text);
    min-width: 120px;
}

.skill-card__certification a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.skill-card__certification a:hover {
    color: var(--color-primary-dark);
    text-decoration: underline;
}

.skills__stats {
    background-color: var(--color-surface);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
}

.skills__stats h3 {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    color: var(--color-text);
}

.stats__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.stats__card {
    text-align: center;
    padding: var(--spacing-md);
    background-color: var(--color-background);
    border-radius: var(--radius-md);
    transition: transform var(--transition-fast);
}

.stats__card:hover {
    transform: translateY(-4px);
}

.stats__card h4 {
    font-size: 1rem;
    color: var(--color-text-light);
    margin-bottom: var(--spacing-sm);
}

.stats__number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--color-primary);
    margin-bottom: var(--spacing-xs);
}

.stats__label {
    font-size: 0.875rem;
    color: var(--color-text-light);
}

/* Responsive Tasarım */
@media (max-width: 768px) {
    .skills__filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter__button {
        width: 100%;
    }

    .skills__grid {
        grid-template-columns: 1fr;
    }

    .stats__grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .stats__grid {
        grid-template-columns: 1fr;
    }
}

/* Login Sayfası Stilleri */
.login-container {
    max-width: 400px;
    margin: 100px auto;
    padding: 30px;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.login-container h2 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #555;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.btn-primary {
    width: 100%;
    padding: 12px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.alert {
    padding: 10px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.alert-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.back-link {
    text-align: center;
    margin-top: 20px;
}

.back-link a {
    color: #007bff;
    text-decoration: none;
}

.back-link a:hover {
    text-decoration: underline;
}