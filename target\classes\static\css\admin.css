/* Admin Panel Stilleri */
:root {
    --admin-primary: #2563eb;
    --admin-primary-dark: #1d4ed8;
    --admin-secondary: #64748b;
    --admin-success: #22c55e;
    --admin-danger: #ef4444;
    --admin-warning: #f59e0b;
    --admin-info: #3b82f6;
    --admin-light: #f8fafc;
    --admin-dark: #1e293b;
    --admin-border: #e2e8f0;
    --admin-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --admin-radius: 0.5rem;
    --admin-transition: all 0.2s ease-in-out;
}

/* Admin Navigasyon */
.admin-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    background-color: var(--admin-dark);
    color: var(--admin-light);
    padding: 1.5rem;
    overflow-y: auto;
    z-index: 1000;
}

.admin-nav__container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.admin-nav__logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--admin-light);
    text-decoration: none;
    margin-bottom: 2rem;
}

.admin-nav__logo i {
    font-size: 1.5rem;
    color: var(--admin-primary);
}

.admin-nav__menu {
    list-style: none;
    padding: 0;
    margin: 0;
    margin-bottom: auto;
}

.admin-nav__link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--admin-light);
    text-decoration: none;
    border-radius: var(--admin-radius);
    transition: var(--admin-transition);
}

.admin-nav__link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.admin-nav__link--active {
    background-color: var(--admin-primary);
}

.admin-nav__link i {
    width: 1.25rem;
    text-align: center;
}

.admin-nav__actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-nav__logout {
    margin: 0;
}

.admin-nav__logout button {
    width: 100%;
    background: none;
    border: none;
    cursor: pointer;
    text-align: left;
    font: inherit;
    color: inherit;
}

/* Ana İçerik */
.admin-main {
    margin-left: 280px;
    padding: 2rem;
    min-height: 100vh;
    background-color: var(--admin-light);
}

.admin-header {
    margin-bottom: 2rem;
}

.admin-header__top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.admin-header h1 {
    font-size: 1.875rem;
    font-weight: 600;
    color: var(--admin-dark);
    margin: 0;
}

.text-secondary {
    color: var(--admin-secondary);
    margin: 0;
}

/* Butonlar */
.admin-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1.25rem;
    background-color: var(--admin-primary);
    color: white;
    border: none;
    border-radius: var(--admin-radius);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--admin-transition);
}

.admin-button:hover {
    background-color: var(--admin-primary-dark);
}

.admin-button--secondary {
    background-color: var(--admin-secondary);
}

.admin-button--secondary:hover {
    background-color: var(--admin-dark);
}

/* Tablo */
.admin-table-wrapper {
    background-color: white;
    border-radius: var(--admin-radius);
    box-shadow: var(--admin-shadow);
    overflow: hidden;
}

.admin-table__filters {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background-color: var(--admin-light);
    border-bottom: 1px solid var(--admin-border);
}

.admin-table__search-wrapper {
    position: relative;
    flex: 1;
}

.admin-table__search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--admin-secondary);
}

.admin-table__search {
    width: 100%;
    padding: 0.625rem 1rem 0.625rem 2.5rem;
    border: 1px solid var(--admin-border);
    border-radius: var(--admin-radius);
    font-size: 0.875rem;
}

.admin-table__filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.admin-table__filter-group label {
    font-size: 0.875rem;
    color: var(--admin-secondary);
}

.admin-table__filter {
    padding: 0.625rem 2rem 0.625rem 1rem;
    border: 1px solid var(--admin-border);
    border-radius: var(--admin-radius);
    font-size: 0.875rem;
    background-color: white;
    cursor: pointer;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th {
    padding: 1rem;
    text-align: left;
    font-weight: 500;
    color: var(--admin-secondary);
    background-color: var(--admin-light);
    border-bottom: 1px solid var(--admin-border);
}

.admin-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--admin-border);
}

.admin-table__sort {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    font: inherit;
    color: inherit;
    cursor: pointer;
}

.admin-table__sort i {
    font-size: 0.75rem;
}

.admin-table__drag-handle {
    color: var(--admin-secondary);
    cursor: move;
    margin-right: 0.5rem;
}

.admin-table__skill {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.admin-table__skill-icon {
    font-size: 1.25rem;
    color: var(--admin-primary);
}

.admin-table__level {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.admin-table__level-bar {
    flex: 1;
    height: 0.5rem;
    background-color: var(--admin-border);
    border-radius: 1rem;
    overflow: hidden;
}

.admin-table__level-fill {
    height: 100%;
    background-color: var(--admin-primary);
    border-radius: 1rem;
}

.admin-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.admin-badge--success {
    background-color: #dcfce7;
    color: var(--admin-success);
}

.admin-badge--danger {
    background-color: #fee2e2;
    color: var(--admin-danger);
}

.admin-table__actions {
    display: flex;
    gap: 0.5rem;
}

.admin-table__button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border: none;
    border-radius: var(--admin-radius);
    color: white;
    cursor: pointer;
    transition: var(--admin-transition);
}

.admin-table__button--edit {
    background-color: var(--admin-info);
}

.admin-table__button--edit:hover {
    background-color: #2563eb;
}

.admin-table__button--delete {
    background-color: var(--admin-danger);
}

.admin-table__button--delete:hover {
    background-color: #dc2626;
}

/* Form */
.admin-form-wrapper {
    max-width: 800px;
    margin: 0 auto;
}

.admin-form {
    background-color: white;
    padding: 2rem;
    border-radius: var(--admin-radius);
    box-shadow: var(--admin-shadow);
}

.admin-form__group {
    margin-bottom: 1.5rem;
}

.admin-form__label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--admin-dark);
}

.admin-form__input,
.admin-form__select,
.admin-form__textarea {
    width: 100%;
    padding: 0.625rem 1rem;
    border: 1px solid var(--admin-border);
    border-radius: var(--admin-radius);
    font-size: 0.875rem;
    transition: var(--admin-transition);
}

.admin-form__input:focus,
.admin-form__select:focus,
.admin-form__textarea:focus {
    outline: none;
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.admin-form__icon-input {
    position: relative;
    display: flex;
    align-items: center;
}

.admin-form__icon-preview {
    position: absolute;
    right: 1rem;
    font-size: 1.25rem;
    color: var(--admin-primary);
}

.admin-form__help {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: var(--admin-secondary);
}

.admin-form__link {
    color: var(--admin-primary);
    text-decoration: none;
}

.admin-form__link:hover {
    text-decoration: underline;
}

.admin-form__range {
    width: 100%;
    margin: 0.5rem 0;
}

.admin-form__range-value {
    text-align: center;
    font-size: 0.875rem;
    color: var(--admin-secondary);
}

.admin-form__checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.admin-form__checkbox input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    border: 1px solid var(--admin-border);
    border-radius: 0.25rem;
    cursor: pointer;
}

.admin-form__error {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: var(--admin-danger);
}

.admin-form__actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

/* Responsive */
@media (max-width: 1024px) {
    .admin-nav {
        width: 240px;
    }
    
    .admin-main {
        margin-left: 240px;
    }
}

@media (max-width: 768px) {
    .admin-nav {
        width: 100%;
        height: auto;
        position: relative;
    }
    
    .admin-nav__container {
        flex-direction: row;
        align-items: center;
        gap: 1rem;
    }
    
    .admin-nav__logo {
        margin-bottom: 0;
    }
    
    .admin-nav__menu {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 0;
    }
    
    .admin-nav__actions {
        flex-direction: row;
        padding-top: 0;
        border-top: none;
        border-left: 1px solid rgba(255, 255, 255, 0.1);
        padding-left: 1rem;
    }
    
    .admin-main {
        margin-left: 0;
        padding: 1rem;
    }
    
    .admin-header__top {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .admin-table__filters {
        flex-direction: column;
    }
    
    .admin-form__actions {
        flex-direction: column;
    }
    
    .admin-form__actions .admin-button {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 640px) {
    .admin-nav__container {
        flex-direction: column;
    }
    
    .admin-nav__menu {
        flex-direction: column;
        width: 100%;
    }
    
    .admin-nav__actions {
        flex-direction: column;
        width: 100%;
        border-left: none;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding-top: 1rem;
        padding-left: 0;
    }
    
    .admin-table {
        display: block;
        overflow-x: auto;
    }
} 