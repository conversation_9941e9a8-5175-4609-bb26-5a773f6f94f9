spring.application.name=personal-website

# SQLite Veritabanı Yapılandırması
spring.datasource.url=*******************************
spring.datasource.driver-class-name=org.sqlite.JDBC
spring.jpa.database-platform=com.ilkeradanur.personal_website.dialect.SQLiteDialect

# JPA/Hibernate Yapılandırması
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# SQL dosyasını otomatik çalıştırma ayarları
spring.sql.init.mode=always
spring.jpa.defer-datasource-initialization=true

# Sunucu Yapılandırması
server.port=8080
server.servlet.context-path=/
server.error.include-message=always
server.error.include-binding-errors=always

# Thymeleaf Yapılandırması
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.mode=HTML

# Statik Kaynaklar
spring.web.resources.static-locations=classpath:/static/
spring.mvc.static-path-pattern=/static/**

# Loglama Yapılandırması
logging.level.org.springframework=INFO
logging.level.com.ilkeradanur=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Dosya Yükleme Yapılandırması
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
