// Admin Panel JavaScript

// Tablo sıralama işlevi
document.addEventListener('DOMContentLoaded', function() {
    const sortButtons = document.querySelectorAll('.admin-table__sort');
    
    sortButtons.forEach(button => {
        button.addEventListener('click', function() {
            const table = this.closest('table');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const index = parseInt(this.dataset.sort);
            const direction = this.dataset.direction === 'asc' ? -1 : 1;
            
            // Sıralama yönünü güncelle
            this.dataset.direction = direction === 1 ? 'asc' : 'desc';
            
            // Sıralama ikonunu güncelle
            const icon = this.querySelector('i');
            icon.className = direction === 1 ? 'fas fa-sort-up' : 'fas fa-sort-down';
            
            // Satırları sırala
            rows.sort((a, b) => {
                const aValue = a.cells[index].textContent.trim();
                const bValue = b.cells[index].textContent.trim();
                
                // Sayısal değerler için özel sıralama
                if (!isNaN(aValue) && !isNaN(bValue)) {
                    return direction * (parseFloat(aValue) - parseFloat(bValue));
                }
                
                // Tarih değerleri için özel sıralama
                if (isValidDate(aValue) && isValidDate(bValue)) {
                    return direction * (new Date(aValue) - new Date(bValue));
                }
                
                // Metin değerleri için normal sıralama
                return direction * aValue.localeCompare(bValue, 'tr');
            });
            
            // Sıralanmış satırları tabloya ekle
            rows.forEach(row => tbody.appendChild(row));
        });
    });
});

// Tarih formatı kontrolü
function isValidDate(dateString) {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
}

// Form doğrulama işlevi
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('.admin-form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            let isValid = true;
            const requiredFields = form.querySelectorAll('[required]');
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('admin-form__input--error');
                    
                    // Hata mesajı oluştur
                    let errorMessage = field.parentElement.querySelector('.admin-form__error');
                    if (!errorMessage) {
                        errorMessage = document.createElement('div');
                        errorMessage.className = 'admin-form__error';
                        field.parentElement.appendChild(errorMessage);
                    }
                    errorMessage.textContent = 'Bu alan zorunludur.';
                } else {
                    field.classList.remove('admin-form__input--error');
                    const errorMessage = field.parentElement.querySelector('.admin-form__error');
                    if (errorMessage) {
                        errorMessage.textContent = '';
                    }
                }
            });
            
            if (!isValid) {
                event.preventDefault();
            }
        });
        
        // Input alanları için anlık doğrulama
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('input', function() {
                if (this.hasAttribute('required')) {
                    if (!this.value.trim()) {
                        this.classList.add('admin-form__input--error');
                    } else {
                        this.classList.remove('admin-form__input--error');
                        const errorMessage = this.parentElement.querySelector('.admin-form__error');
                        if (errorMessage) {
                            errorMessage.textContent = '';
                        }
                    }
                }
            });
        });
    });
});

// Dosya yükleme önizleme işlevi
document.addEventListener('DOMContentLoaded', function() {
    const fileInputs = document.querySelectorAll('input[type="file"]');
    
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const preview = this.parentElement.querySelector('.admin-form__preview');
            if (preview && this.files && this.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    if (preview.tagName === 'IMG') {
                        preview.src = e.target.result;
                    } else {
                        preview.style.backgroundImage = `url(${e.target.result})`;
                    }
                    preview.style.display = 'block';
                };
                
                reader.readAsDataURL(this.files[0]);
            }
        });
    });
});

// Bildirim işlevi
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `admin-notification admin-notification--${type}`;
    notification.innerHTML = `
        <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
        <span>${message}</span>
    `;
    
    document.body.appendChild(notification);
    
    // Bildirimi göster
    setTimeout(() => {
        notification.classList.add('admin-notification--show');
    }, 100);
    
    // Bildirimi kaldır
    setTimeout(() => {
        notification.classList.remove('admin-notification--show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// Onay kutusu işlevi
function showConfirm(message, callback) {
    const confirmBox = document.createElement('div');
    confirmBox.className = 'admin-confirm';
    confirmBox.innerHTML = `
        <div class="admin-confirm__content">
            <p>${message}</p>
            <div class="admin-confirm__actions">
                <button type="button" class="admin-button admin-button--secondary admin-confirm__cancel">
                    İptal
                </button>
                <button type="button" class="admin-button admin-confirm__confirm">
                    Onayla
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(confirmBox);
    
    // Onay kutusunu göster
    setTimeout(() => {
        confirmBox.classList.add('admin-confirm--show');
    }, 100);
    
    // İptal butonu
    confirmBox.querySelector('.admin-confirm__cancel').addEventListener('click', () => {
        confirmBox.classList.remove('admin-confirm--show');
        setTimeout(() => {
            confirmBox.remove();
        }, 300);
    });
    
    // Onay butonu
    confirmBox.querySelector('.admin-confirm__confirm').addEventListener('click', () => {
        callback();
        confirmBox.classList.remove('admin-confirm--show');
        setTimeout(() => {
            confirmBox.remove();
        }, 300);
    });
}

// Sayfa yüklendiğinde bildirimleri kontrol et
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const message = urlParams.get('message');
    const type = urlParams.get('type') || 'success';
    
    if (message) {
        showNotification(decodeURIComponent(message), type);
        
        // URL'den parametreleri kaldır
        const newUrl = window.location.pathname + window.location.hash;
// Form gönderimi
document.querySelectorAll('.admin-form').forEach(form => {
    form.addEventListener('submit', async (e) => {
        e.preventDefault();

        if (!validateForm(form)) {
            alert('Lütfen tüm gerekli alanları doldurun.');
            return;
        }

        const formData = new FormData(form);
        const submitButton = form.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;

        try {
            submitButton.disabled = true;
            submitButton.textContent = 'Kaydediliyor...';

            const response = await fetch(form.action, {
                method: form.method,
                body: formData
            });

            if (!response.ok) {
                throw new Error('Bir hata oluştu.');
            }

            const result = await response.json();

            if (result.success) {
                alert('Değişiklikler başarıyla kaydedildi.');
                if (result.redirect) {
                    window.location.href = result.redirect;
                }
            } else {
                throw new Error(result.message || 'Bir hata oluştu.');
            }
        } catch (error) {
            alert(error.message);
        } finally {
            submitButton.disabled = false;
            submitButton.textContent = originalText;
        }
    });
});

// Silme işlemi onayı
document.querySelectorAll('.admin-table__button--delete').forEach(button => {
    button.addEventListener('click', (e) => {
        if (!confirm('Bu öğeyi silmek istediğinizden emin misiniz?')) {
            e.preventDefault();
        }
    });
});

// Dosya yükleme önizleme
document.querySelectorAll('input[type="file"]').forEach(input => {
    input.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            const preview = document.querySelector(`#${input.id}-preview`);

            if (preview) {
                reader.onload = (e) => {
                    if (file.type.startsWith('image/')) {
                        preview.innerHTML = `<img src="${e.target.result}" alt="Önizleme">`;
                    } else {
                        preview.textContent = file.name;
                    }
                };
                reader.readAsDataURL(file);
            }
        }
    });
});

// Sıralama ve filtreleme
document.querySelectorAll('.admin-table__sort').forEach(button => {
    button.addEventListener('click', () => {
        const table = button.closest('table');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const index = button.dataset.sort;
        const direction = button.dataset.direction === 'asc' ? -1 : 1;

        rows.sort((a, b) => {
            const aValue = a.children[index].textContent;
            const bValue = b.children[index].textContent;
            return direction * aValue.localeCompare(bValue, 'tr');
        });

        button.dataset.direction = direction === 1 ? 'asc' : 'desc';
        button.querySelector('i').className = direction === 1 ? 'fas fa-sort-up' : 'fas fa-sort-down';

        tbody.append(...rows);
    });
});

// Arama filtresi
document.querySelectorAll('.admin-table__search').forEach(input => {
    input.addEventListener('input', (e) => {
        const table = input.closest('.admin-table-wrapper').querySelector('table');
        const searchTerm = e.target.value.toLowerCase();
        const rows = table.querySelectorAll('tbody tr');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
});

// Tema değiştirme
const themeToggle = document.querySelector('.admin-nav__theme-toggle');
if (themeToggle) {
    themeToggle.addEventListener('click', () => {
        document.body.classList.toggle('dark-theme');
        const icon = themeToggle.querySelector('i');
        icon.className = document.body.classList.contains('dark-theme') ? 'fas fa-sun' : 'fas fa-moon';
    });
}

// Mobil menü
const menuToggle = document.querySelector('.admin-nav__menu-toggle');
if (menuToggle) {
    menuToggle.addEventListener('click', () => {
        const menu = document.querySelector('.admin-nav__menu');
        menu.classList.toggle('admin-nav__menu--active');
    });
}

// Sayfa yüklendiğinde
document.addEventListener('DOMContentLoaded', () => {
    // Aktif menü öğesini işaretle
    const currentPath = window.location.pathname;
    document.querySelectorAll('.admin-nav__link').forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('admin-nav__link--active');
        }
    });

    // Form alanlarını otomatik kaydet
    let saveTimeout;
    document.querySelectorAll('.admin-form__input, .admin-form__textarea').forEach(input => {
        input.addEventListener('input', () => {
            clearTimeout(saveTimeout);
            saveTimeout = setTimeout(() => {
                const form = input.closest('form');
                if (form && form.dataset.autoSave === 'true') {
                    form.dispatchEvent(new Event('submit'));
                }
            }, 1000);
        });
    });
}); 