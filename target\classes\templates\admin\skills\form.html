<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title}">Yetenek Ekle/Düzenle</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/images/favicon.png">
    
    <!-- Fontlar -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/admin.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
</head>
<body>
    <!-- Admin Navigasyon -->
    <nav class="admin-nav">
        <div class="admin-nav__container">
            <a href="/admin/dashboard" class="admin-nav__logo">
                <i class="fas fa-user-shield"></i>
                Admin Paneli
            </a>
            
            <ul class="admin-nav__menu">
                <li>
                    <a href="/admin/about" class="admin-nav__link">
                        <i class="fas fa-user"></i>
                        Hakkımda
                    </a>
                </li>
                <li>
                    <a href="/admin/skills" class="admin-nav__link admin-nav__link--active">
                        <i class="fas fa-code"></i>
                        Yetenekler
                    </a>
                </li>
                <li>
                    <a href="/admin/projects" class="admin-nav__link">
                        <i class="fas fa-project-diagram"></i>
                        Projeler
                    </a>
                </li>
                <li>
                    <a href="/admin/timeline" class="admin-nav__link">
                        <i class="fas fa-calendar-alt"></i>
                        Zaman Çizelgesi
                    </a>
                </li>
            </ul>
            
            <div class="admin-nav__actions">
                <a href="/" class="admin-nav__link" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    Siteyi Görüntüle
                </a>
                <form th:action="@{/logout}" method="post" class="admin-nav__logout">
                    <button type="submit" class="admin-nav__link">
                        <i class="fas fa-sign-out-alt"></i>
                        Çıkış Yap
                    </button>
                </form>
            </div>
        </div>
    </nav>

    <!-- Ana İçerik -->
    <main class="admin-main">
        <div class="container">
            <div class="admin-header">
                <div class="admin-header__top">
                    <h1 th:text="${title}">Yetenek Ekle/Düzenle</h1>
                    <a href="/admin/skills" class="admin-button admin-button--secondary">
                        <i class="fas fa-arrow-left"></i>
                        Geri Dön
                    </a>
                </div>
                <p class="text-secondary">Yetenek bilgilerini düzenleyin.</p>
            </div>
            
            <div class="admin-form-wrapper">
                <form th:action="@{/admin/skills/save}" th:object="${skill}" method="post" class="admin-form">
                    <input type="hidden" th:field="*{id}">
                    
                    <div class="admin-form__group">
                        <label for="name" class="admin-form__label">Yetenek Adı</label>
                        <input type="text" 
                               id="name" 
                               th:field="*{name}" 
                               class="admin-form__input" 
                               required>
                        <div class="admin-form__error" th:if="${#fields.hasErrors('name')}" th:errors="*{name}"></div>
                    </div>
                    
                    <div class="admin-form__group">
                        <label for="category" class="admin-form__label">Kategori</label>
                        <select id="category" 
                                th:field="*{category}" 
                                class="admin-form__select" 
                                required>
                            <option value="">Kategori Seçin</option>
                            <option value="programming">Programlama</option>
                            <option value="framework">Framework</option>
                            <option value="database">Veritabanı</option>
                            <option value="tool">Araç</option>
                            <option value="language">Dil</option>
                            <option value="certification">Sertifika</option>
                        </select>
                        <div class="admin-form__error" th:if="${#fields.hasErrors('category')}" th:errors="*{category}"></div>
                    </div>
                    
                    <div class="admin-form__group">
                        <label for="icon" class="admin-form__label">İkon (Font Awesome Sınıfı)</label>
                        <div class="admin-form__icon-input">
                            <input type="text" 
                                   id="icon" 
                                   th:field="*{icon}" 
                                   class="admin-form__input" 
                                   placeholder="Örn: fab fa-java">
                            <i th:if="${skill.icon}" th:class="${skill.icon}" class="admin-form__icon-preview"></i>
                        </div>
                        <div class="admin-form__help">
                            <a href="https://fontawesome.com/icons" target="_blank" class="admin-form__link">
                                <i class="fas fa-external-link-alt"></i>
                                Font Awesome İkonları
                            </a>
                        </div>
                        <div class="admin-form__error" th:if="${#fields.hasErrors('icon')}" th:errors="*{icon}"></div>
                    </div>
                    
                    <div class="admin-form__group">
                        <label for="level" class="admin-form__label">Seviye (%)</label>
                        <input type="range" 
                               id="level" 
                               th:field="*{level}" 
                               class="admin-form__range" 
                               min="0" 
                               max="100" 
                               step="5" 
                               required>
                        <div class="admin-form__range-value">
                            <span id="level-value" th:text="${skill.level ?: 0}">0</span>%
                        </div>
                        <div class="admin-form__error" th:if="${#fields.hasErrors('level')}" th:errors="*{level}"></div>
                    </div>
                    
                    <div class="admin-form__group">
                        <label for="description" class="admin-form__label">Açıklama</label>
                        <textarea id="description" 
                                  th:field="*{description}" 
                                  class="admin-form__textarea" 
                                  rows="3"></textarea>
                        <div class="admin-form__error" th:if="${#fields.hasErrors('description')}" th:errors="*{description}"></div>
                    </div>
                    
                    <div class="admin-form__group">
                        <label class="admin-form__label">Durum</label>
                        <div class="admin-form__checkbox">
                            <input type="checkbox" 
                                   id="isActive" 
                                   th:field="*{isActive}">
                            <label for="isActive">Aktif</label>
                        </div>
                        <div class="admin-form__error" th:if="${#fields.hasErrors('isActive')}" th:errors="*{isActive}"></div>
                    </div>
                    
                    <div class="admin-form__actions">
                        <button type="submit" class="admin-button">
                            <i class="fas fa-save"></i>
                            Kaydet
                        </button>
                        <a href="/admin/skills" class="admin-button admin-button--secondary">
                            <i class="fas fa-times"></i>
                            İptal
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="/js/admin.js"></script>
    <script th:inline="javascript">
        // Seviye değeri güncelleme
        const levelInput = document.getElementById('level');
        const levelValue = document.getElementById('level-value');
        
        levelInput.addEventListener('input', function() {
            levelValue.textContent = this.value;
        });
        
        // İkon önizleme
        const iconInput = document.getElementById('icon');
        const iconPreview = document.querySelector('.admin-form__icon-preview');
        
        iconInput.addEventListener('input', function() {
            if (this.value) {
                iconPreview.className = this.value + ' admin-form__icon-preview';
                iconPreview.style.display = 'inline-block';
            } else {
                iconPreview.style.display = 'none';
            }
        });
    </script>
</body>
</html> 