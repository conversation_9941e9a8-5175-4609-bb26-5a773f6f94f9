<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title}">Yetenekler</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/images/favicon.png">
    
    <!-- Fontlar -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/admin.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
</head>
<body>
    <!-- Admin Navigasyon -->
    <nav class="admin-nav">
        <div class="admin-nav__container">
            <a href="/admin/dashboard" class="admin-nav__logo">
                <i class="fas fa-user-shield"></i>
                Admin Paneli
            </a>
            
            <ul class="admin-nav__menu">
                <li>
                    <a href="/admin/about" class="admin-nav__link">
                        <i class="fas fa-user"></i>
                        Hakkımda
                    </a>
                </li>
                <li>
                    <a href="/admin/skills" class="admin-nav__link admin-nav__link--active">
                        <i class="fas fa-code"></i>
                        Yetenekler
                    </a>
                </li>
                <li>
                    <a href="/admin/projects" class="admin-nav__link">
                        <i class="fas fa-project-diagram"></i>
                        Projeler
                    </a>
                </li>
                <li>
                    <a href="/admin/timeline" class="admin-nav__link">
                        <i class="fas fa-calendar-alt"></i>
                        Zaman Çizelgesi
                    </a>
                </li>
            </ul>
            
            <div class="admin-nav__actions">
                <a href="/" class="admin-nav__link" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    Siteyi Görüntüle
                </a>
                <form th:action="@{/logout}" method="post" class="admin-nav__logout">
                    <button type="submit" class="admin-nav__link">
                        <i class="fas fa-sign-out-alt"></i>
                        Çıkış Yap
                    </button>
                </form>
            </div>
        </div>
    </nav>

    <!-- Ana İçerik -->
    <main class="admin-main">
        <div class="container">
            <div class="admin-header">
                <div class="admin-header__top">
                    <h1 th:text="${title}">Yetenekler</h1>
                    <a href="/admin/skills/create" class="admin-button">
                        <i class="fas fa-plus"></i>
                        Yeni Yetenek
                    </a>
                </div>
                <p class="text-secondary">Yeteneklerinizi ekleyin, düzenleyin veya kaldırın.</p>
            </div>
            
            <div class="admin-table-wrapper">
                <div class="admin-table__filters">
                    <div class="admin-table__search-wrapper">
                        <i class="fas fa-search admin-table__search-icon"></i>
                        <input type="text" class="admin-table__search" placeholder="Yetenek ara...">
                    </div>
                    
                    <div class="admin-table__filter-group">
                        <label for="category-filter">Kategori:</label>
                        <select id="category-filter" class="admin-table__filter">
                            <option value="">Tümü</option>
                            <option value="programming">Programlama</option>
                            <option value="framework">Framework</option>
                            <option value="database">Veritabanı</option>
                            <option value="tool">Araç</option>
                            <option value="language">Dil</option>
                            <option value="certification">Sertifika</option>
                        </select>
                    </div>
                </div>
                
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>
                                <button class="admin-table__sort" data-sort="0" data-direction="asc">
                                    <i class="fas fa-sort"></i>
                                    Sıra
                                </button>
                            </th>
                            <th>
                                <button class="admin-table__sort" data-sort="1" data-direction="asc">
                                    <i class="fas fa-sort"></i>
                                    Yetenek
                                </button>
                            </th>
                            <th>
                                <button class="admin-table__sort" data-sort="2" data-direction="asc">
                                    <i class="fas fa-sort"></i>
                                    Kategori
                                </button>
                            </th>
                            <th>
                                <button class="admin-table__sort" data-sort="3" data-direction="asc">
                                    <i class="fas fa-sort"></i>
                                    Seviye
                                </button>
                            </th>
                            <th>Durum</th>
                            <th>İşlemler</th>
                        </tr>
                    </thead>
                    <tbody id="sortable">
                        <tr th:each="skill : ${skills}" th:data-id="${skill.id}">
                            <td>
                                <i class="fas fa-grip-vertical admin-table__drag-handle"></i>
                                <span th:text="${skill.displayOrder}">1</span>
                            </td>
                            <td>
                                <div class="admin-table__skill">
                                    <i th:if="${skill.icon}" th:class="${skill.icon}" class="admin-table__skill-icon"></i>
                                    <span th:text="${skill.name}">Yetenek Adı</span>
                                </div>
                            </td>
                            <td th:text="${skill.category}">Kategori</td>
                            <td>
                                <div class="admin-table__level">
                                    <div class="admin-table__level-bar">
                                        <div class="admin-table__level-fill" th:style="'width: ' + ${skill.level} + '%'"></div>
                                    </div>
                                    <span th:text="${skill.level}">80</span>%
                                </div>
                            </td>
                            <td>
                                <span th:class="${skill.isActive ? 'admin-badge admin-badge--success' : 'admin-badge admin-badge--danger'}"
                                      th:text="${skill.isActive ? 'Aktif' : 'Pasif'}">
                                    Aktif
                                </span>
                            </td>
                            <td>
                                <div class="admin-table__actions">
                                    <a th:href="@{/admin/skills/edit/{id}(id=${skill.id})}" 
                                       class="admin-table__button admin-table__button--edit"
                                       title="Düzenle">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button"
                                            class="admin-table__button admin-table__button--delete"
                                            th:data-id="${skill.id}"
                                            title="Sil">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script src="/js/admin.js"></script>
    <script th:inline="javascript">
        // Thymeleaf değişkenlerini JavaScript'e aktar
        const skills = /*[[${skills}]]*/ [];
        
        // Sıralama işlevi
        const sortable = new Sortable(document.getElementById('sortable'), {
            handle: '.admin-table__drag-handle',
            animation: 150,
            onEnd: function() {
                const skillIds = Array.from(sortable.el.querySelectorAll('tr'))
                    .map(tr => tr.dataset.id);
                
                fetch('/admin/skills/order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(skillIds)
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        // Sıralama başarılı
                        const rows = sortable.el.querySelectorAll('tr');
                        rows.forEach((row, index) => {
                            row.querySelector('td:first-child span').textContent = index + 1;
                        });
                    } else {
                        alert(result.message);
                    }
                })
                .catch(error => {
                    console.error('Sıralama güncellenirken hata:', error);
                    alert('Sıralama güncellenirken bir hata oluştu.');
                });
            }
        });
        
        // Silme işlevi
        document.querySelectorAll('.admin-table__button--delete').forEach(button => {
            button.addEventListener('click', function() {
                if (confirm('Bu yeteneği silmek istediğinizden emin misiniz?')) {
                    const id = this.dataset.id;
                    
                    fetch(`/admin/skills/delete/${id}`, {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            this.closest('tr').remove();
                        } else {
                            alert(result.message);
                        }
                    })
                    .catch(error => {
                        console.error('Silme işlemi sırasında hata:', error);
                        alert('Silme işlemi sırasında bir hata oluştu.');
                    });
                }
            });
        });
        
        // Filtreleme işlevi
        const categoryFilter = document.getElementById('category-filter');
        const searchInput = document.querySelector('.admin-table__search');
        
        function filterTable() {
            const category = categoryFilter.value;
            const searchTerm = searchInput.value.toLowerCase();
            
            document.querySelectorAll('.admin-table tbody tr').forEach(row => {
                const skillName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                const skillCategory = row.querySelector('td:nth-child(3)').textContent;
                
                const matchesCategory = !category || skillCategory === category;
                const matchesSearch = !searchTerm || skillName.includes(searchTerm);
                
                row.style.display = matchesCategory && matchesSearch ? '' : 'none';
            });
        }
        
        categoryFilter.addEventListener('change', filterTable);
        searchInput.addEventListener('input', filterTable);
    </script>
</body>
</html> 